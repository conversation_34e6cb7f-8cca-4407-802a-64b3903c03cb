ig.module('game.entities.objects.popup-level-select')
.requires(
    'plugins.utils.objects.popup-base',
    'game.entities.text',
    'game.entities.buttons.button-nav',
    'game.data.level-data'
)
.defines(function () {

    EntityPopupLevelSelect = EntityPopupBase.extend({
        name: 'popup-level-select',

        // --- Configuration for the Level Select Popup ---

        // Override default header text settings from EntityPopupBase
        headerTextConfig: {
            text: '',
            fontSize: 90,
            fontFamily: 'bebasneue-bold',
            fontColor: '#FFFFFF',
            align: 'center',
            vAlign: 'middle',
            shadowEnabled: true,
            shadowOffsetX: 4,
            shadowOffsetY: 4,
            shadowBlur: 1,
            shadowColor: '#000000',
            overflow: true
        },

        // Configuration for the subtitle text
        subtitleTextConfig: {
            text: '',
            fontSize: 42,
            fontFamily: 'bebasneue-bold',
            fontColor: '#CCCCCC',
            align: 'center',
            vAlign: 'middle',
            shadowEnabled: true,
            shadowOffsetX: 2,
            shadowOffsetY: 2,
            shadowBlur: 1,
            shadowColor: '#000000',
            overflow: true
        },

        // Offset for the header text relative to the popup's center
        headerTextOffset: { x: 0, y: 20 },

        // Offset for the subtitle text relative to the popup's center
        subtitleTextOffset: { x: 0, y: 100 },

        // Configuration for the page indicator text
        pageIndicatorTextConfig: {
            text: '1/3',
            fontSize: 28,
            fontFamily: 'bebasneue-bold',
            fontColor: '#AAAAAA',
            align: 'center',
            vAlign: 'middle',
            shadowEnabled: true,
            shadowOffsetX: 1,
            shadowOffsetY: 1,
            shadowBlur: 1,
            shadowColor: '#000000',
            overflow: true
        },

        // Offset for the page indicator text relative to the popup's center
        pageIndicatorTextOffset: { x: 0, y: -70 },

        displayOverlay: false,

        hasCloseButton: true,

        thumbnailList: [
            new ig.Image('media/graphics/sprites/maps/map-1-preview.png'),
            new ig.Image('media/graphics/sprites/maps/map-2-preview.png'),
            new ig.Image('media/graphics/sprites/maps/map-3-preview.png')
        ],

        thumbnailImage: null,

        currentThumbnailIndex: 0,

        thumbnailBounds: null,

        bestScores: {},

        // Thumbnail transition properties
        thumbnailAlpha: 1,
        isTransitioning: false,
        transitionDuration: 200,

        init: function (x, y, settings) {
            this.parent(x, y, settings);

            this.elements.buttons = {};
            this.elements.buttons.next = this.spawnEntity(
                EntityButtonNext,
                this.pos.x,
                this.pos.y
            );
            this.elements.buttons.prev = this.spawnEntity(
                EntityButtonPrev,
                this.pos.x,
                this.pos.y
            );

            this.bestScores = ig.game.load('bestScore') || {};

            this.levelDataManager = ig.currentCtrl.levelDataManager;
            this.thumbnailImage = this.thumbnailList[this.currentThumbnailIndex];
            this.elements.headerText.setTextContent(this.levelDataManager.getLevelData("level1").title);

            // Create subtitle text element
            this.createSubtitleText();
            var bestScore = this.bestScores['level' + (this.currentThumbnailIndex + 1)] || 0;
            this.elements.subtitleText.setTextContent(_STRINGS['Game']['BestScore'] + bestScore);

            // Create page indicator text element
            this.createPageIndicatorText();

            this.ctx = ig.system.context;

            // Update button states based on initial index
            this.updateButtonStates();

            ig.game.sortEntitiesDeferred();
        },

        createSubtitleText: function () {
            // Create subtitle text configuration
            var currentSubtitleTextConfig = {};
            ig.merge(currentSubtitleTextConfig, this.subtitleTextConfig);

            // Set dynamic width/height for the subtitle text box
            currentSubtitleTextConfig.width = currentSubtitleTextConfig.width || this.size.x;
            currentSubtitleTextConfig.height = currentSubtitleTextConfig.height || (this.size.y * 0.15);

            // Spawn the EntityText for the subtitle
            var subtitleTextEntitySettings = {
                textConfig: currentSubtitleTextConfig,
                alpha: this.popupAlpha,
                zIndex: this.zIndex + 1
            };

            // Calculate initial position for the subtitle text entity
            var initialSubtitleX = this.pos.x + (this.subtitleTextOffset.x || 0);
            var initialSubtitleY = this.pos.y + (this.subtitleTextOffset.y || 0);

            this.elements.subtitleText = ig.game.spawnEntity(
                EntityText,
                initialSubtitleX,
                initialSubtitleY,
                subtitleTextEntitySettings
            );
        },

        createPageIndicatorText: function () {
            // Create page indicator text configuration
            var currentPageIndicatorTextConfig = {};
            ig.merge(currentPageIndicatorTextConfig, this.pageIndicatorTextConfig);

            // Set dynamic width/height for the page indicator text box
            currentPageIndicatorTextConfig.width = currentPageIndicatorTextConfig.width || this.size.x;
            currentPageIndicatorTextConfig.height = currentPageIndicatorTextConfig.height || (this.size.y * 0.1);

            // Set initial page indicator text
            currentPageIndicatorTextConfig.text = (this.currentThumbnailIndex + 1) + '/' + this.thumbnailList.length;

            // Spawn the EntityText for the page indicator
            var pageIndicatorTextEntitySettings = {
                textConfig: currentPageIndicatorTextConfig,
                alpha: this.popupAlpha,
                zIndex: this.zIndex + 1
            };

            // Calculate initial position for the page indicator text entity
            var initialPageIndicatorX = this.pos.x + (this.pageIndicatorTextOffset.x || 0);
            var initialPageIndicatorY = this.pos.y + (this.pageIndicatorTextOffset.y || 0);

            this.elements.pageIndicatorText = ig.game.spawnEntity(
                EntityText,
                initialPageIndicatorX,
                initialPageIndicatorY,
                pageIndicatorTextEntitySettings
            );
        },

        onNext: function () {
            // Only proceed if not at the last item and not currently transitioning
            if (this.currentThumbnailIndex < this.thumbnailList.length - 1 && !this.isTransitioning) {
                this.transitionToThumbnail(this.currentThumbnailIndex + 1);
            }
        },

        onPrev: function () {
            // Only proceed if not at the first item and not currently transitioning
            if (this.currentThumbnailIndex > 0 && !this.isTransitioning) {
                this.transitionToThumbnail(this.currentThumbnailIndex - 1);
            }
        },

        transitionToThumbnail: function (newIndex) {
            if (this.isTransitioning) return;

            this.isTransitioning = true;
            var self = this;

            // Fade out current thumbnail
            var fadeOutTween = { alpha: this.thumbnailAlpha };
            new ig.TweenDef(fadeOutTween)
            .to({ alpha: 0 }, this.transitionDuration)
            .onUpdate(function () {
                self.thumbnailAlpha = fadeOutTween.alpha;
            })
            .onComplete(function () {
                // Switch to new thumbnail
                self.currentThumbnailIndex = newIndex;
                self.thumbnailImage = self.thumbnailList[self.currentThumbnailIndex];

                // Update text content
                var bestScore = self.bestScores['level' + (self.currentThumbnailIndex + 1)] || 0;
                self.elements.headerText.setTextContent(self.levelDataManager.getLevelData("level" + (self.currentThumbnailIndex + 1)).title);
                self.elements.subtitleText.setTextContent(_STRINGS['Game']['BestScore'] + bestScore);
                self.elements.pageIndicatorText.setTextContent((self.currentThumbnailIndex + 1) + '/' + self.thumbnailList.length);
                self.updateButtonStates();

                // Fade in new thumbnail
                var fadeInTween = { alpha: 0 };
                new ig.TweenDef(fadeInTween)
                .to({ alpha: 1 }, self.transitionDuration)
                .onUpdate(function () {
                    self.thumbnailAlpha = fadeInTween.alpha;
                })
                .onComplete(function () {
                    self.thumbnailAlpha = 1;
                    self.isTransitioning = false;
                })
                .start();
            })
            .start();
        },

        updateButtonStates: function () {
            if (this.elements.buttons) {
                // Disable prev button if at first item
                if (this.currentThumbnailIndex <= 0) {
                    this.elements.buttons.prev.hide();
                } else {
                    this.elements.buttons.prev.show();
                }

                // Disable next button if at last item
                if (this.currentThumbnailIndex >= this.thumbnailList.length - 1) {
                    this.elements.buttons.next.hide();
                } else {
                    this.elements.buttons.next.show();
                }
            }
        },

        calculateThumbnailBounds: function () {
            if (!this.thumbnailImage) return;

            var thumbnailX = this.pos.x + this.size.x * 0.5 - this.thumbnailImage.width * 0.5;
            var thumbnailY = this.pos.y + this.size.y - this.thumbnailImage.height - 80;

            this.thumbnailBounds = {
                x: thumbnailX,
                y: thumbnailY,
                width: this.thumbnailImage.width,
                height: this.thumbnailImage.height
            };
        },

        isClickInThumbnail: function (clickX, clickY) {
            if (!this.thumbnailBounds) return false;

            return clickX >= this.thumbnailBounds.x &&
                   clickX <= this.thumbnailBounds.x + this.thumbnailBounds.width &&
                   clickY >= this.thumbnailBounds.y &&
                   clickY <= this.thumbnailBounds.y + this.thumbnailBounds.height;
        },

        onSelect: function () {
            if (this.isTransitioning) return;
            if (ig.soundHandler.sfxPlayer.soundList.click) {
                ig.soundHandler.sfxPlayer.play('click');
            }
            ig.game.currentLevel = this.currentThumbnailIndex + 1;
            ig.currentCtrl.transition.fadeIn(500, function () {
                ig.game.director.jumpTo(LevelGame);
            });
        },

        updateElementsAlpha: function (alpha) {
            this.parent(alpha); // Call parent method to update header, close button, overlay

            if (this.elements.buttons) {
                for (var key in this.elements.buttons) {
                    if (this.elements.buttons[key] && typeof this.elements.buttons[key].updateAlpha === 'function') {
                        this.elements.buttons[key].updateAlpha(alpha);
                    }
                }
            }

            // Update subtitle text alpha
            if (this.elements.subtitleText && typeof this.elements.subtitleText.updateAlpha === 'function') {
                this.elements.subtitleText.updateAlpha(alpha);
            }

            // Update page indicator text alpha
            if (this.elements.pageIndicatorText && typeof this.elements.pageIndicatorText.updateAlpha === 'function') {
                this.elements.pageIndicatorText.updateAlpha(alpha);
            }
        },

        updateElementsPosition: function () {
            this.parent(); // Call parent method to update header, close button

            if (this.elements.buttons) {
                // position next and prev buttons at the sides of the popup
                var padding = 20;
                this.elements.buttons.next.pos.x = this.pos.x + this.size.x - this.elements.buttons.next.size.x - padding;
                this.elements.buttons.next.pos.y = this.pos.y + this.size.y * 0.5;

                this.elements.buttons.prev.pos.x = this.pos.x + padding;
                this.elements.buttons.prev.pos.y = this.elements.buttons.next.pos.y;
            }

            if (this.elements.headerText) this.elements.headerText._updateAnchorPosition();

            // Update subtitle text position
            if (this.elements.subtitleText) {
                this.elements.subtitleText.pos.x = this.pos.x + (this.subtitleTextOffset.x || 0);
                this.elements.subtitleText.pos.y = this.pos.y + (this.subtitleTextOffset.y || 0);
                if (this.elements.subtitleText._updateAnchorPosition) {
                    this.elements.subtitleText._updateAnchorPosition();
                }
            }

            // Update page indicator text position
            if (this.elements.pageIndicatorText) {
                this.elements.pageIndicatorText.pos.x = this.pos.x + (this.pageIndicatorTextOffset.x || 0);
                this.elements.pageIndicatorText.pos.y = this.pos.y + this.size.y + (this.pageIndicatorTextOffset.y || 0);
                if (this.elements.pageIndicatorText._updateAnchorPosition) {
                    this.elements.pageIndicatorText._updateAnchorPosition();
                }
            }
        },

        exitCb: function () {
            ig.currentCtrl.tweenShow();
        },

        kill: function () {
            if (this.elements.buttons) {
                this.elements.buttons.next.kill();
                this.elements.buttons.prev.kill();
            }
            // Kill subtitle text
            if (this.elements.subtitleText) {
                this.elements.subtitleText.kill();
            }
            // Kill page indicator text
            if (this.elements.pageIndicatorText) {
                this.elements.pageIndicatorText.kill();
            }
            // Kill other custom buttons if added
            this.parent();
        },

        update: function () {
            this.parent();
            this.calculateThumbnailBounds();

            // Check for thumbnail click
            if (ig.input.released('click') && !this.isTweening) {
                var mouse = ig.game.io.getClickPos();

                if (this.isClickInThumbnail(mouse.x, mouse.y)) {
                    this.onSelect();
                }
            }
        },

        draw: function () {
            this.parent();
            this.ctx.save();
            this.ctx.globalAlpha = this.popupAlpha * this.thumbnailAlpha;
            if (this.thumbnailImage) {
                // When updating the values below, ensure they match the thumbnailBounds calculation in calculateThumbnailBounds
                this.thumbnailImage.draw(this.pos.x + this.size.x * 0.5 - this.thumbnailImage.width * 0.5, this.pos.y + this.size.y - this.thumbnailImage.height - 80);
            }
            this.ctx.restore();
        }
    });
});
