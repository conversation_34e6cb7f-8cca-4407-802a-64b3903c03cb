<!DOCTYPE html>
<html>
<head>
    <title>Truck Path Extension Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        #gameCanvas {
            border: 2px solid #333;
            background-color: white;
            display: block;
            margin: 20px auto;
            cursor: crosshair;
        }
        
        .info {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-case {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f8f8;
            border-left: 4px solid #4CAF50;
        }
        
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            padding: 10px 20px;
            margin: 0 5px;
            font-size: 16px;
            cursor: pointer;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        .status {
            text-align: center;
            font-size: 18px;
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="info">
        <h1>Truck Path Extension Ownership Test</h1>
        <p>This test demonstrates the fix for the path extension bug where trucks could accidentally extend each other's paths.</p>
        
        <div class="test-case">
            <h3>Test Scenario 1: Two Trucks with Close Endpoints</h3>
            <p>Two trucks (Red and Blue) have paths that end near each other. Click near the endpoints to test that only the truck with the closest endpoint can extend its path.</p>
        </div>
        
        <div class="test-case">
            <h3>Test Scenario 2: Multiple Trucks Converging</h3>
            <p>Three trucks converge to a central area. Test that each truck maintains ownership of its own path.</p>
        </div>
        
        <div class="controls">
            <button onclick="runTest1()">Run Test 1</button>
            <button onclick="runTest2()">Run Test 2</button>
            <button onclick="resetTest()">Reset</button>
        </div>
        
        <div id="status" class="status"></div>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>
    
    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const status = document.getElementById('status');
        
        // Mock truck objects for testing
        class MockTruck {
            constructor(id, color, startPos) {
                this.id = id;
                this.color = color;
                this.pos = { ...startPos };
                this.trajectory = [];
                this.pathExtensionRadius = 50;
                this.isMoving = false;
                this.vel = { x: 0, y: 0 };
            }
            
            addPathPoint(point) {
                this.trajectory.push({ ...point });
                this.isMoving = true;
                // Simulate movement
                if (this.trajectory.length > 1) {
                    const prev = this.trajectory[this.trajectory.length - 2];
                    const curr = this.trajectory[this.trajectory.length - 1];
                    const dx = curr.x - prev.x;
                    const dy = curr.y - prev.y;
                    const dist = Math.sqrt(dx * dx + dy * dy);
                    this.vel.x = (dx / dist) * 50;
                    this.vel.y = (dy / dist) * 50;
                }
            }
            
            getPathEnd() {
                return this.trajectory.length > 0 ? 
                    this.trajectory[this.trajectory.length - 1] : 
                    this.pos;
            }
            
            isClickNearPathEnd(clickPos) {
                if (this.trajectory.length === 0 || !this.isMoving) return false;
                const pathEnd = this.getPathEnd();
                const distance = Math.sqrt(
                    Math.pow(clickPos.x - pathEnd.x, 2) + 
                    Math.pow(clickPos.y - pathEnd.y, 2)
                );
                return distance <= this.pathExtensionRadius;
            }
            
            draw() {
                // Draw truck
                ctx.fillStyle = this.color;
                ctx.fillRect(this.pos.x - 20, this.pos.y - 30, 40, 60);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(this.id, this.pos.x, this.pos.y);
                
                // Draw path
                if (this.trajectory.length > 0) {
                    ctx.strokeStyle = this.color;
                    ctx.lineWidth = 4;
                    ctx.beginPath();
                    ctx.moveTo(this.pos.x, this.pos.y);
                    for (const point of this.trajectory) {
                        ctx.lineTo(point.x, point.y);
                    }
                    ctx.stroke();
                    
                    // Draw endpoint
                    const end = this.getPathEnd();
                    ctx.fillStyle = this.color;
                    ctx.beginPath();
                    ctx.arc(end.x, end.y, 8, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // Draw extension radius
                    ctx.strokeStyle = this.color + '40';
                    ctx.lineWidth = 2;
                    ctx.setLineDash([5, 5]);
                    ctx.beginPath();
                    ctx.arc(end.x, end.y, this.pathExtensionRadius, 0, Math.PI * 2);
                    ctx.stroke();
                    ctx.setLineDash([]);
                }
            }
        }
        
        let trucks = [];
        let selectedTruck = null;
        
        function findClosestTruck(clickPos) {
            let closestTruck = null;
            let closestDistance = Infinity;
            
            for (const truck of trucks) {
                if (!truck.isMoving || truck.trajectory.length === 0) continue;
                
                const pathEnd = truck.getPathEnd();
                const distance = Math.sqrt(
                    Math.pow(clickPos.x - pathEnd.x, 2) + 
                    Math.pow(clickPos.y - pathEnd.y, 2)
                );
                
                if (distance <= truck.pathExtensionRadius && distance < closestDistance) {
                    closestDistance = distance;
                    closestTruck = truck;
                }
            }
            
            return closestTruck;
        }
        
        function runTest1() {
            resetTest();
            status.textContent = 'Test 1: Click near the overlapping path endpoints to extend paths';
            status.className = 'status';
            
            // Create two trucks with paths that end near each other
            const truck1 = new MockTruck('T1', 'red', { x: 100, y: 300 });
            truck1.addPathPoint({ x: 200, y: 300 });
            truck1.addPathPoint({ x: 300, y: 300 });
            truck1.addPathPoint({ x: 380, y: 300 });
            
            const truck2 = new MockTruck('T2', 'blue', { x: 700, y: 300 });
            truck2.addPathPoint({ x: 600, y: 300 });
            truck2.addPathPoint({ x: 500, y: 300 });
            truck2.addPathPoint({ x: 420, y: 300 });
            
            trucks = [truck1, truck2];
            draw();
        }
        
        function runTest2() {
            resetTest();
            status.textContent = 'Test 2: Three trucks converging - click to extend individual paths';
            status.className = 'status';
            
            // Create three trucks converging to center
            const truck1 = new MockTruck('T1', 'red', { x: 400, y: 100 });
            truck1.addPathPoint({ x: 400, y: 200 });
            truck1.addPathPoint({ x: 400, y: 280 });
            
            const truck2 = new MockTruck('T2', 'blue', { x: 200, y: 450 });
            truck2.addPathPoint({ x: 280, y: 400 });
            truck2.addPathPoint({ x: 360, y: 340 });
            
            const truck3 = new MockTruck('T3', 'green', { x: 600, y: 450 });
            truck3.addPathPoint({ x: 520, y: 400 });
            truck3.addPathPoint({ x: 440, y: 340 });
            
            trucks = [truck1, truck2, truck3];
            draw();
        }
        
        function resetTest() {
            trucks = [];
            selectedTruck = null;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            status.textContent = 'Select a test scenario above';
            status.className = 'status';
        }
        
        function draw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw grid
            ctx.strokeStyle = '#e0e0e0';
            ctx.lineWidth = 1;
            for (let x = 0; x < canvas.width; x += 50) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }
            for (let y = 0; y < canvas.height; y += 50) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }
            
            // Draw trucks
            for (const truck of trucks) {
                truck.draw();
            }
            
            // Highlight selected truck
            if (selectedTruck) {
                const end = selectedTruck.getPathEnd();
                ctx.strokeStyle = '#00ff00';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.arc(end.x, end.y, 15, 0, Math.PI * 2);
                ctx.stroke();
            }
        }
        
        canvas.addEventListener('click', (e) => {
            const rect = canvas.getBoundingClientRect();
            const clickPos = {
                x: e.clientX - rect.left,
                y: e.clientY - rect.top
            };
            
            // Find the closest truck to the click
            const closestTruck = findClosestTruck(clickPos);
            
            if (closestTruck) {
                selectedTruck = closestTruck;
                closestTruck.addPathPoint(clickPos);
                status.textContent = `Extended path for ${closestTruck.id} (${closestTruck.color} truck)`;
                status.className = 'status success';
                draw();
            } else {
                selectedTruck = null;
                status.textContent = 'No truck path endpoint within extension radius';
                status.className = 'status error';
                draw();
            }
        });
        
        canvas.addEventListener('mousemove', (e) => {
            const rect = canvas.getBoundingClientRect();
            const mousePos = {
                x: e.clientX - rect.left,
                y: e.clientY - rect.top
            };
            
            // Check if mouse is near any path endpoint
            let nearTruck = null;
            for (const truck of trucks) {
                if (truck.isClickNearPathEnd(mousePos)) {
                    nearTruck = truck;
                    break;
                }
            }
            
            canvas.style.cursor = nearTruck ? 'pointer' : 'crosshair';
        });
        
        // Initialize
        resetTest();
    </script>
</body>
</html>