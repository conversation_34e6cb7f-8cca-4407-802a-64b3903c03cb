ig.module(
    'game.controllers.spawn-controller'
)
.requires(
    'impact.impact',
    'game.data.level-data',
    'game.entities.warning-signal',
    'game.entities.spawn-grid',
    'game.entities.objects.truck'
)
.defines(function () {

    SpawnController = ig.Class.extend({
        levelData: null,
        currentLevelId: null,
        truckSpawnAreas: [],
        occupiedSpawnAreas: [], // Tracks spawn areas currently in use by a spawning sequence.
        spawningDisabled: false,
        spawnInterval: 5000, // Time in milliseconds between truck spawn attempts.
        spawnTimer: null,
        parkingSlots: null,
        availableParkingColors: null,
        isFirstSpawn: true,
        init: function (gameController) {
            this.spawnTimer = new ig.Timer();
            this.spawnTimer.set(1); // Set initial timer to trigger first spawn check quickly.

            this.levelData = gameController.currentLevelData;
            this.currentLevelId = gameController.currentLevelId;
            this.occupiedSpawnAreas = [];

            if (!this.levelData || !this.currentLevelId) {
                console.error("SpawnController: LevelData or CurrentLevelId not available from gameController.");
                this.spawningDisabled = true;
                return;
            }

            // Ensure truckSpawnAreas is initialized, even if empty.
            this.truckSpawnAreas = this.levelData.truckSpawnAreas || [];
            if (this.truckSpawnAreas.length === 0) {
                console.warn("SpawnController: No truckSpawnAreas defined for level: " + this.currentLevelId);
                // Note: Spawning might not be strictly disabled here if areas could be added later,
                // but for now, it effectively won't spawn anything.
            }
        },

        pauseGame: function () {
            if (this.spawnTimer) this.spawnTimer.pause();
            var warningSignals = ig.game.getEntitiesByType(EntityWarningSignal);
            for (var i = 0; i < warningSignals.length; i++) {
                warningSignals[i].pauseBlinking();
            }
        },

        resumeGame: function () {
            if (this.spawnTimer) this.spawnTimer.unpause();
            var warningSignals = ig.game.getEntitiesByType(EntityWarningSignal);
            for (var i = 0; i < warningSignals.length; i++) {
                warningSignals[i].resumeBlinking();
            }
        },

        update: function () {
            if (this.spawningDisabled || ig.game.isPaused || ig.currentCtrl.isGameOver) {
                return;
            }

            if (this.spawnTimer && this.spawnTimer.delta() > 0) {
                this.spawnTruckSequence();
                this.spawnTimer = null;
            }
        },

        /**
         * Retrieves the world coordinates (x, y) and spawn direction for a named spawn area.
         * Spawn areas are typically visualised by EntitySpawnGrid text objects.
         * @param {string} areaName - The unique name of the spawn area (e.g., "top1", "left2").
         * @returns {Object|null} An object {x, y, dir} where dir is an integer (0:Left, 1:Top, 2:Right, 3:Bottom),
         *                        or null if the areaName is not found or EntitySpawnGrid is missing.
         */
        getSpawnPosition: function (areaName) {
            var spawnGridArray = ig.game.getEntitiesByType(EntitySpawnGrid);
            if (!spawnGridArray || spawnGridArray.length === 0) {
                console.error("SpawnController: EntitySpawnGrid not found. Cannot determine spawn positions.");
                return null;
            }
            var spawnGrid = spawnGridArray[0]; // Assuming a single SpawnGrid entity.

            if (!spawnGrid.allTextObj || spawnGrid.allTextObj.length === 0) {
                console.error("SpawnController: EntitySpawnGrid has no text objects (allTextObj) to define spawn points.");
                return null;
            }

            // Map text object collections from SpawnGrid to their corresponding spawn directions.
            var pointsAndDirs = [
                { texts: spawnGrid.topText, dir: 1 },    // Top edge points, trucks spawn moving down.
                { texts: spawnGrid.botText, dir: 3 },    // Bottom edge points, trucks spawn moving up.
                { texts: spawnGrid.leftText, dir: 0 },  // Left edge points, trucks spawn moving right.
                { texts: spawnGrid.rightText, dir: 2 } // Right edge points, trucks spawn moving left.
            ];

            // Find the text object matching areaName to get its position.
            for (var i = 0; i < spawnGrid.allTextObj.length; i++) {
                var textObj = spawnGrid.allTextObj[i];
                if (textObj.name === areaName) {
                    var dir = null;
                    // Determine direction based on which SpawnGrid array contained this areaName.
                    for (var j = 0; j < pointsAndDirs.length; j++) {
                        if (pointsAndDirs[j].texts.indexOf(textObj.name) !== -1) {
                            dir = pointsAndDirs[j].dir;
                            break;
                        }
                    }
                    if (dir === null) {
                        console.warn("SpawnController: Spawn area '" + areaName + "' found, but its direction couldn't be determined.");
                        return null;
                    }
                    return { x: textObj.pos.x, y: textObj.pos.y, dir: dir };
                }
            }

            console.warn("SpawnController: Spawn area name '" + areaName + "' not found in EntitySpawnGrid.");
            return null;
        },

        /**
         * Manages the process of spawning a truck:
         * 1. Selects an available (non-occupied) spawn area.
         * 2. Marks the area as occupied.
         * 3. Spawns a warning signal at the chosen location.
         * 4. After the warning, spawns the actual truck and unmarks the area.
         * Resets the spawn timer for the next truck.
         */
        spawnTruckSequence: function () {
            if (this.spawningDisabled) {
                // console.log("SpawnController: Spawning is disabled.");
                return;
            }

            if (!this.truckSpawnAreas || this.truckSpawnAreas.length === 0) {
                // console.warn("SpawnController: No truck spawn areas defined.");
                return;
            }

            // Identify spawn areas not currently part of an active spawning sequence.
            var availableSpawnAreas = this.truckSpawnAreas.filter(function (area) {
                return this.occupiedSpawnAreas.indexOf(area) === -1;
            }, this);

            if (availableSpawnAreas.length === 0) {
                // console.log("SpawnController: All truck spawn areas are currently occupied or no areas available.");
                // Might consider resetting the spawn timer here
                return;
            }

            var randomIndex = Math.floor(Math.random() * availableSpawnAreas.length);
            var randomAreaName = availableSpawnAreas[randomIndex];
            var spawnPosition = this.getSpawnPosition(randomAreaName);

            if (!spawnPosition) {
                console.error("SpawnController: Could not get spawn position for area '" + randomAreaName + "'. Aborting this truck spawn.");
                return;
            }

            // Temporarily mark this spawn area as "in use" to prevent other trucks from spawning here simultaneously.
            this.occupiedSpawnAreas.push(randomAreaName);
            // console.log("SpawnController: Area '" + randomAreaName + "' marked as occupied. Occupied areas: ", this.occupiedSpawnAreas);

            var warningSignal = ig.game.spawnEntity(EntityWarningSignal, spawnPosition.x, spawnPosition.y, {
                blinkCount: this.isFirstSpawn ? 3 : 6,
                blinkDuration: 1, // Duration of the warning in seconds before truck appears.
                direction: spawnPosition.dir
            });

            if (!warningSignal) {
                console.error("SpawnController: Failed to spawn EntityWarningSignal for area '" + randomAreaName + "'.");
                // If warning signal fails, unmark the area to allow future spawns.
                var indexToRemove = this.occupiedSpawnAreas.indexOf(randomAreaName);
                if (indexToRemove > -1) {
                    this.occupiedSpawnAreas.splice(indexToRemove, 1);
                }
                // Might consider resetting the spawn timer here
                return;
            }

            var self = this;
            warningSignal.onBlinkingComplete = function () {
                self.spawnActualTruck(randomAreaName);
                self.spawnTimer = new ig.Timer(self.spawnInterval / 1000);
                self.isFirstSpawn = false;
                // Release the spawn area from its "occupied" state.
                var indexToRemove = self.occupiedSpawnAreas.indexOf(randomAreaName);
                if (indexToRemove > -1) {
                    self.occupiedSpawnAreas.splice(indexToRemove, 1);
                    // console.log("SpawnController: Area '" + randomAreaName + "' unmarked. Occupied areas: ", self.occupiedSpawnAreas);
                } else {
                    // This case should ideally not happen if logic is correct.
                    console.warn("SpawnController: Tried to unmark area '" + randomAreaName + "' but it was not in the occupied list.");
                }
                this.kill();
            };
        },

        /**
         * Spawns an EntityTruck at the specified spawn area.
         * The truck's initial position and movement will be determined by its `spawnPoint` setting.
         * @param {string} areaName - The name of the spawn area (e.g., "top1").
         */
        spawnActualTruck: function (areaName) {
            if (!areaName) {
                console.error("SpawnController: Invalid area name provided for spawning truck.");
                return;
            }
            var colorAndSlot = this.assignColor(areaName);
            var truck = ig.game.spawnEntity(EntityTruck, 0, 0, { // Initial x,y are trivial; truck repositions based on spawnPoint.
                spawnPoint: areaName,
                color: colorAndSlot.color,
                intendedParkingSlotName: colorAndSlot.intendedParkingSlotName
            });

            if (truck) {
                ig.currentCtrl.setupTruckCollision(truck); // Register the new truck with the game controller for collision handling.
                // console.log("SpawnController: EntityTruck spawned at area: " + areaName);
            } else {
                console.error("SpawnController: Failed to spawn EntityTruck at area: " + areaName);
            }
        },

        assignColor: function (areaName) {
            if (!this.availableParkingColors && !this.parkingSlots) {
                this.availableParkingColors = [];
                this.parkingSlots = ig.game.getEntitiesByType(EntityParkingSlot);
                for (var i = 0; i < this.parkingSlots.length; i++) {
                    var slot = this.parkingSlots[i];
                    if (slot.color && this.availableParkingColors.indexOf(slot.color) === -1) {
                        this.availableParkingColors.push(slot.color);
                    }
                }
            }
            var color = null;
            if (this.availableParkingColors.length > 0) {
                var specialCase = this.handleSpecialSpawnCase(areaName);
                if (specialCase) {
                    color = specialCase[Math.floor(Math.random() * specialCase.length)];
                } else {
                    color = this.availableParkingColors[Math.floor(Math.random() * this.availableParkingColors.length)];
                }
            }

            this.handleSpecialSpawnCase();

            var matchingSlots = this.parkingSlots.filter(function (slot) {
                return slot.color === color;
            }, this);
            if (matchingSlots.length > 0) {
                this.intendedParkingSlotName = matchingSlots[0].name;
            } else {
                this.intendedParkingSlotName = null;
            }

            return {
                color: color,
                intendedParkingSlotName: this.intendedParkingSlotName
            };
        },

        handleSpecialSpawnCase: function (areaName) {
            // For level3, make sure that truck colors red and green only spawn on the left side (topLeft, top1, left1, botLeft, bot1)
            // and colors blue and yellow spawn on the right side (top10, topRight, right, bot10, botRight)
            // also make sure the spawnPoint is unoccupied
            if (ig.currentCtrl.currentLevelId === 'level3') {
                var leftSideAreas = ['topLeft', 'top1', 'left1', 'botLeft', 'bot1'];
                var rightSideAreas = ['top10', 'topRight', 'right1', 'bot10', 'botRight'];
                var leftSideColors = ['red', 'green'];
                var rightSideColors = ['blue', 'yellow'];
                
                // Copy availableParkingColors
                var availableColors = ig.copy(this.availableParkingColors);
                
                // If areaName is provided, restrict colors based on spawn area location
                if (areaName) {
                    if (leftSideAreas.indexOf(areaName) !== -1) {
                        // Left side spawn area - only allow red and green
                        availableColors = availableColors.filter(function (color) {
                            return leftSideColors.indexOf(color) !== -1;
                        });
                    } else if (rightSideAreas.indexOf(areaName) !== -1) {
                        // Right side spawn area - only allow blue and yellow
                        availableColors = availableColors.filter(function (color) {
                            return rightSideColors.indexOf(color) !== -1;
                        });
                    }
                    return availableColors;
                }
                
                // If no specific areaName provided, filter based on available areas
                var availableLeftAreas = this.truckSpawnAreas.filter(function (area) {
                    return leftSideAreas.indexOf(area) !== -1 && this.occupiedSpawnAreas.indexOf(area) === -1;
                }, this);
                
                var availableRightAreas = this.truckSpawnAreas.filter(function (area) {
                    return rightSideAreas.indexOf(area) !== -1 && this.occupiedSpawnAreas.indexOf(area) === -1;
                }, this);
                
                // Adjust available colors based on available spawn areas
                if (availableLeftAreas.length === 0) {
                    // If no left areas available, remove left-side colors
                    availableColors = availableColors.filter(function (color) {
                        return leftSideColors.indexOf(color) === -1;
                    });
                } else if (availableRightAreas.length === 0) {
                    // If no right areas available, remove right-side colors
                    availableColors = availableColors.filter(function (color) {
                        return rightSideColors.indexOf(color) === -1;
                    });
                }
                
                return availableColors;
            }
            
            // For other levels, return undefined (no special handling)
            return undefined;
        }
    });

});

