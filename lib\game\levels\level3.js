ig.module(
    'game.levels.level3'
)
.defines(function (){

Level3MapData = {
    id: 'level3', // Unique ID for this specific level data object
    title: 'The Bridge', // Placeholder title
    image: "map-3", // Key to look up the image in mapImageList
    truckSpawnAreas: ['topLeft', 'top1', 'top10', 'topRight', 'left1', 'botLeft', 'right1', 'botRight', 'bot1', 'bot10'],
    buildings: [
        {
            name: 'building1', // Top Building
            type: 1,
            vertices: [{ x: 608.38, y: 0.00 }, { x: 1361.43, y: 0.00 }, { x: 1374.48, y: 475.18 }, { x: 609.56, y: 488.23 }]
        },
        { 
            name: 'building2', // Bottom Building
            type: 1,
            vertices: [{ x: 684.27, y: 801.68 }, { x: 1317.55, y: 801.68 }, { x: 1321.11, y: 1076.44 }, { x: 677.16, y: 1080.00 }]
        },
        {
            name: 'middle-river',
            type: 1,
            vertices: [{ x: 309.52, y: 442.35 }, { x: 659.37, y: 442.35 }, { x: 1437.33, y: 474.00 }, { x: 1600.40, y: 436.64 }, { x: 1596.24, y: 809.61 }, { x: 1404.72, y: 773.03 }, { x: 682.79, y: 750.00 }, { x: 311.16, y: 810.82 }]
        },
        { 
            name: 'left-river',
            type: 1,
            vertices: [{ x: -0.00, y: 291.74 }, { x: 107.92, y: 396.10 }, { x: 109.10, y: 825.03 }, { x: 1.19, y: 852.30 }]
        },
        { 
            name: 'right-river',
            type: 1,
            vertices: [{ x: 1799.04, y: 366.45 }, { x: 1917.63, y: 300.04 }, { x: 1918.81, y: 806.05 }, { x: 1801.41, y: 804.87 }]
        },
        { 
            name: 'bush-bottom',
            type: 1,
            vertices: [{ x: 354.59, y: 764.92 }, { x: 431.67, y: 750.69 }, { x: 448.28, y: 816.73 }, { x: 409.14, y: 849.93 }, { x: 355.78, y: 826.99 }]
        },
        { 
            name: 'crane',
            type: 1,
            vertices: [{ x: 602.45, y: 730.53 }, { x: 686.65, y: 761.36 }, { x: 610.75, y: 832.14 }, { x: 547.89, y: 776.00 }]
        },
        { 
            name: 'box-bottom',
            type: 1,
            vertices: [{ x: 1316.37, y: 787.45 }, { x: 1414.80, y: 787.45 }, { x: 1417.17, y: 866.53 }, { x: 1317.55, y: 868.50 }]
        },
        // --- Parking Slots ---
        {
            name: 'parking-slot-1-building1', // Top Building
            type: 3,
            collidesWith: [4],
            vertices: [{ x: 370.99, y: 49.02 }, { x: 604.82, y: 49.41 }, { x: 604.82, y: 149.05 }, { x: 370.80, y: 149.44 }],
            color: GameColors.RED,
            colorIndicator: {
                anchor: { x: 0.5, y: 0 },
                anchorOffset: { x: 0, y: 0 }
            }
            // Swap the frontPoint and backPoint if the automatic calculation is incorrect.
            // swapPoints: true
            // To manually setup the points, uncomment the following lines and adjust the coordinates as needed.
            // frontPoint: { x: 1585.89, y: 741.57 },
            // backPoint: { x: 1586.68, y: 972.52 }
        },
        {
            name: 'parking-slot-2-building1',
            type: 3,
            collidesWith: [4],
            vertices: [{ x: 1364.00, y: 48.23 }, { x: 1598.62, y: 48.62 }, { x: 1597.83, y: 149.05 }, { x: 1364.60, y: 149.04 }],
            color: GameColors.BLUE,
            colorIndicator: {
                anchor: { x: 0.5, y: 0 },
                anchorOffset: { x: 0, y: 0 }
            },
            swapPoints: true
        },
        {
            name: 'parking-slot-1-building2',
            type: 3,
            collidesWith: [4],
            vertices: [{ x: 443.73, y: 956.64 }, { x: 675.97, y: 957.83 }, { x: 675.58, y: 1056.68 }, { x: 443.53, y: 1057.46 }],
            color: GameColors.GREEN,
            colorIndicator: {
                anchor: { x: 0.5, y: 0 },
                anchorOffset: { x: 0, y: 0 }
            }
        },
        {
            name: 'parking-slot-2-building2',
            type: 3,
            collidesWith: [4],
            vertices: [{ x: 1325.46, y: 957.23 }, { x: 1559.48, y: 956.64 }, { x: 1558.49, y: 1056.68 }, { x: 1325.86, y: 1056.27 }],
            color: GameColors.YELLOW,
            colorIndicator: {
                anchor: { x: 0.5, y: 0 },
                anchorOffset: { x: 0, y: 0 }
            }
        }
        // {
        //     name: 'parking-slot-1-building3',
        //     type: 3,
        //     collidesWith: [4],
        //     vertices: [{ x: 1489.39, y: 235.60 }, { x: 1489.87, y: 134.64 }, { x: 1723.85, y: 134.98 }, { x: 1722.78, y: 234.09 }],
        //     color: GameColors.PURPLE,
        //     colorIndicator: {
        //         anchor: { x: 0, y: 0.5 },
        //         anchorOffset: { x: 0, y: 0 }
        //     }
        // },
        // {
        //     name: 'parking-slot-2-building3',
        //     type: 3,
        //     collidesWith: [4],
        //     vertices: [{ x: 1489.79, y: 446.30 }, { x: 1489.47, y: 346.13 }, { x: 1722.66, y: 346.08 }, { x: 1723.18, y: 445.97 }],
        //     color: GameColors.ORANGE,
        //     colorIndicator: {
        //         anchor: { x: 0, y: 0.5 },
        //         anchorOffset: { x: 0, y: 0 }
        //     }
        // }
        // Add more collision objects here as needed
    ]
};

});
